public class AesUtilTest {
    public static void main(String[] args) {
        // 模拟 AesUtil 的核心逻辑进行测试
        System.out.println("=== AES 加密解密测试 ===");
        
        // 测试数据
        String originalText = "421023201502175684";
        System.out.println("原始文本: " + originalText);
        
        // 模拟加密过程（使用标准 Base64）
        String simulatedEncrypted = "BX0exUxXakKENielcp7ZYr06TzcJBLCmP+LRnx6qiyE=";
        System.out.println("模拟加密结果: " + simulatedEncrypted);
        
        // 检查 Base64 编码格式
        System.out.println("\n=== Base64 编码格式分析 ===");
        System.out.println("密文包含 '+' 字符: " + simulatedEncrypted.contains("+"));
        System.out.println("密文包含 '/' 字符: " + simulatedEncrypted.contains("/"));
        System.out.println("密文包含 '-' 字符: " + simulatedEncrypted.contains("-"));
        System.out.println("密文包含 '_' 字符: " + simulatedEncrypted.contains("_"));
        
        if (simulatedEncrypted.contains("+") || simulatedEncrypted.contains("/")) {
            System.out.println("这是标准 Base64 编码");
        } else if (simulatedEncrypted.contains("-") || simulatedEncrypted.contains("_")) {
            System.out.println("这是 URL 安全 Base64 编码");
        } else {
            System.out.println("无法确定 Base64 编码类型");
        }
        
        System.out.println("\n=== 修复说明 ===");
        System.out.println("问题: AESEncrypt 使用标准 Base64 编码，但 AESDecrypt 使用 URL 安全 Base64 解码");
        System.out.println("修复: 统一使用标准 Base64 编码/解码");
        System.out.println("- AESEncrypt: 使用 Base64.encode()");
        System.out.println("- AESDecrypt: 使用 Base64.decode()");
        System.out.println("- AESEncryptURL: 使用 Base64Utils.encodeToUrlSafeString()");
        System.out.println("- AESDecryptURL: 使用 Base64Utils.decodeFromUrlSafeString()");
    }
}
