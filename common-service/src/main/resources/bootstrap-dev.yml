spring:
  cloud:
    nacos:
      discovery:
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        server-addr: ${NACOS_SERVER_IP:************}:${NACOS_SERVER_PORT:8848}
        namespace: ${NACOS_NAMESPACE:9e1db6d0-61cf-40d2-9cc3-978a519f0ecf}
        group: ${NACOS_GROUP:DEV_GROUP}
        metadata:
          preserved.heart.beat.interval: 1000
          preserved.heart.beat.timeout: 3000
          preserved.ip.delete.timeout: 3000
      config:
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${spring.cloud.nacos.discovery.group}
        file-extension: yaml
        # 共享配置
        shared-configs:
          - data-id: base-start-config.yaml
            group: ${spring.cloud.nacos.config.group}
            refresh: true
  #RabbitMq 配置
  rabbitmq:
    host: ${RABBITMQ_HOST:************}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER_NAME:base}
    password: ${RABBITMQ_USER_PWD:lskj2017}
    virtual-host: /
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 5
          initial-interval: 5000ms
          max-interval: 10000ms
          stateless: true
    # 新增的动态配置
    custom:
      enabled: true
      management:
        enabled: true # 启用管理接口
      # 交换机配置
      exchanges:
        # 身份相关交换机
        - name: identify.exchange
          type: direct
          durable: true
        # 通用Topic交换机
        - name: common.topic.exchange
          type: topic
          durable: true
        # 延时消息交换机
        - name: delayed.exchange
          type: x-delayed-message
          durable: true
          delayed-type: direct
        # 死信交换机
        - name: dlx.exchange
          type: direct
          durable: true
          
      # 队列配置
      queues:
        # 身份队列
        - name: identify.queue
          durable: true
          dead-letter-exchange: dlx.exchange
          dead-letter-routing-key: identify.dlq
          max-priority: 10
        # 身份死信队列
        - name: identify.dlq
          durable: true
        # 通用队列
        - name: common.queue
          durable: true
          max-length: 1000
        # 延时队列
        - name: delayed.queue
          durable: true
          
      # 绑定关系配置
      bindings:
        # 身份相关绑定
        - queue: identify.queue
          exchange: identify.exchange
          routing-key: identify.routing.key
        - queue: identify.dlq
          exchange: dlx.exchange
          routing-key: identify.dlq
        # 通用绑定
        - queue: common.queue
          exchange: common.topic.exchange
          routing-key: common.*
        # 延时消息绑定
        - queue: delayed.queue
          exchange: delayed.exchange
          routing-key: delayed.message