<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskDao">

    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask" id="NewStudentTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="taskDesc" column="task_desc" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="BOOLEAN"/>
        <result property="taskContent" column="task_content" jdbcType="VARCHAR"/>
        <result property="taskUrl" column="task_url" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task(name, sort, task_desc, task_status, task_content, task_url, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.sort} , #{entity.taskDesc} , #{entity.taskStatus} , #{entity.taskContent} , #{entity.taskUrl} , #{entity.createTime} , #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task(name, sort, task_desc, task_status, task_content, task_url, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.sort}, #{entity.taskDesc}, #{entity.taskStatus}, #{entity.taskContent}, #{entity.taskUrl}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , sort = EXCLUDED.sort , task_desc = EXCLUDED.task_desc , task_status = EXCLUDED.task_status , task_content = EXCLUDED.task_content , task_url = EXCLUDED.task_url , create_time = EXCLUDED.create_time , update_time = EXCLUDED.update_time     </insert>

</mapper>

