<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentDataDao">

    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData" id="NewStudentDataMap">
        <result property="idCardNum" column="id_card_num" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="phoneNo" column="phone_no" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="authStatus" column="auth_status" jdbcType="BOOLEAN"/>
        <result property="openStatus" column="open_status" jdbcType="BOOLEAN"/>
        <result property="startSendStatus" column="start_send_status" jdbcType="BOOLEAN"/>
        <result property="endSendStatus" column="end_send_status" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="idCardNum" useGeneratedKeys="true">
        insert into addressbook.new_student_data(name, userid, phone_no, year, dept_code, dept_name, auth_status, open_status, start_send_status, end_send_status)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.name} , #{entity.userid} , #{entity.phoneNo} , #{entity.year} , #{entity.deptCode} , #{entity.deptName} , #{entity.authStatus} , #{entity.openStatus} , #{entity.startSendStatus} , #{entity.endSendStatus})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="idCardNum" useGeneratedKeys="true">
        insert into addressbook.new_student_data(name, userid, phone_no, year, dept_code, dept_name, auth_status, open_status, start_send_status, end_send_status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.userid}, #{entity.phoneNo}, #{entity.year}, #{entity.deptCode}, #{entity.deptName}, #{entity.authStatus}, #{entity.openStatus}, #{entity.startSendStatus}, #{entity.endSendStatus})
        </foreach>
        ON CONFLICT(id) DO update set
name = EXCLUDED.name , userid = EXCLUDED.userid , phone_no = EXCLUDED.phone_no , year = EXCLUDED.year , dept_code = EXCLUDED.dept_code , dept_name = EXCLUDED.dept_name , auth_status = EXCLUDED.auth_status , open_status = EXCLUDED.open_status , start_send_status = EXCLUDED.start_send_status , end_send_status = EXCLUDED.end_send_status     </insert>

</mapper>

