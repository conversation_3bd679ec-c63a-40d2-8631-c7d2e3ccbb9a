<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.NewStudentTaskStatusMapper">

    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.addressbook.entity.NewStudentTaskStatusEntity">
        <!--@Table new_student_task_status-->
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="noticeStatus" column="notice_status" jdbcType="INTEGER"/>
            <result property="infoStatus" column="info_status" jdbcType="INTEGER"/>
            <result property="signStatus" column="sign_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateTime" column="last_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
id,
user_id,
notice_status,
info_status,
sign_status,
create_time,
last_update_time

    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task_status(user_id, notice_status, info_status, sign_status, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.userId}, #{entity.noticeStatus}, #{entity.infoStatus}, #{entity.signStatus}, #{entity.createTime}, #{entity.lastUpdateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task_status(user_id, notice_status, info_status, sign_status, create_time, last_update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.noticeStatus}, #{entity.infoStatus}, #{entity.signStatus}, #{entity.createTime}, #{entity.lastUpdateTime})
        </foreach>
        on duplicate key update
user_id = values(user_id) , notice_status = values(notice_status) , info_status = values(info_status) , sign_status = values(sign_status) , create_time = values(create_time) , last_update_time = values(last_update_time)     </insert>

</mapper>

