package com.lanshan.base.commonservice.addressbook.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (NewStudentGroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-20 16:13:42
 */
public interface NewStudentGroupMapper extends BaseMapper<NewStudentGroup> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<NewStudentGroupEntity> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<NewStudentGroup> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<NewStudentGroupEntity> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<NewStudentGroup> entities);

    NewStudentGroup selectByChatId(@Param("chatId") String chatId);

    void insertByme(@Param("entity") NewStudentGroup entity);

    void updateByme(@Param("entity") NewStudentGroup newStudentGroup);
}

