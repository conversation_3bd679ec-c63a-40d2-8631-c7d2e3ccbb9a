package com.lanshan.base.commonservice.welcomenewstudent.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 新生任务状态表(NewStudentTaskStatus)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新生任务状态表VO")
@Data
@ToString
public class NewStudentTaskStatusVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "新生任务id")
    private String newStuTaskId;
}

