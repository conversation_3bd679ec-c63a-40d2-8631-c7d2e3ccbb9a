package com.lanshan.base.commonservice.identify.service.mq.producer;

import com.lanshan.base.commonservice.config.rabbitmq.MqConstant;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * RabbitMQ消息生产者
 * 提供身份相关的消息发送功能
 */
@Slf4j
@Service("rabbitProducer")
public class RabbitProducer {
    
    @Resource
    private EnhancedMqMessageSender enhancedMqMessageSender;
    
    @Value("${message.queueexchange}")
    private String queueexchange;

    /**
     * 发送普通消息
     */
    public void sendMessage(Object message) {
        enhancedMqMessageSender.sendToDirectExchange(
                queueexchange,
                MqConstant.IDENTIFY_ROUTER_KEY,
                message
        );
        log.debug("身份消息发送成功: exchange={}, routingKey={}", queueexchange, MqConstant.IDENTIFY_ROUTER_KEY);
    }

    /**
     * 发送优先级消息
     */
    public void sendPriorityMessage(Object message, int priority) {
        enhancedMqMessageSender.sendMessageWithPriority(
                queueexchange,
                MqConstant.IDENTIFY_ROUTER_KEY,
                message,
                priority
        );
        log.debug("优先级身份消息发送成功: exchange={}, routingKey={}, priority={}", 
            queueexchange, MqConstant.IDENTIFY_ROUTER_KEY, priority);
    }

    /**
     * 发送延时消息
     */
    public void sendDelayedMessage(Object message, long delay, TimeUnit timeUnit) {
        enhancedMqMessageSender.sendDelayedMessage(
                queueexchange,
                MqConstant.IDENTIFY_ROUTER_KEY,
                message,
                delay,
                timeUnit
        );
        log.debug("延时身份消息发送成功: exchange={}, routingKey={}, delay={}ms", 
            queueexchange, MqConstant.IDENTIFY_ROUTER_KEY, timeUnit.toMillis(delay));
    }
}