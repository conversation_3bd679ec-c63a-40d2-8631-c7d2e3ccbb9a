package com.lanshan.base.commonservice.identify.controller;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.access.annotation.ApiCallLog;
import com.lanshan.base.commonservice.access.enums.AppTypeEnum;
import com.lanshan.base.commonservice.identify.dto.*;
import com.lanshan.base.commonservice.identify.service.IdentifyApiService;
import com.lanshan.base.commonservice.identify.service.StaffService;
import com.lanshan.base.commonservice.identify.service.mq.producer.RabbitProducer;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = ServiceConstant.COMMON_IDENTIFY_API)
@Api(tags = "身份对外接口", hidden = true)
@Slf4j
public class IdentifyApiController {

    @Autowired
    private IdentifyApiService identifyApiService;
    @Autowired
    private StaffService staffService;

    @Autowired
    private RabbitProducer producer;
    
    @Autowired
    private EnhancedMqMessageSender enhancedMqMessageSender;

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取ls_userid")
    @GetMapping(value = "getLsUserId")
    public Result<Object> getLsUserId(@Validated IDentifyNumDto iDentifyNumDto) {
        try {
            iDentifyNumDto.verify();
            String lanshanUserId = identifyApiService.getLsUserId(iDentifyNumDto);
            return Result.build(new HashMap<>().put("lanshanUserId", lanshanUserId));
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(10).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取用户身份列表")
    @GetMapping(value = "getIdentifyList")
    public Result<Object> getIdentifyList(@Validated IdentifyListDto identifyListDto) {
        try {
            if (StringUtils.isEmpty(identifyListDto.getLanshanUserId())) {
                throw new ServiceException("请求参数不能为空", 21);
            }
            Map resultMap = new HashMap<String, Object>();
            resultMap.put("lanshanUserId", identifyListDto.getLanshanUserId());
            resultMap.put("identifyList", identifyApiService.getIdentifyList(identifyListDto));
            return Result.build(resultMap);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(20).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "获取当前身份")
    @GetMapping(value = "getCurrentIdentify")
    public Result<Object> getCurrentIdentify(@Validated CurrentIdentifyDto currentIdentifyDto) {
        try {
            if (StringUtils.isEmpty(currentIdentifyDto.getLanshanUserId())) {
                throw new ServiceException("请求参数不能为空", 31);
            }
            Map resultMap = new HashMap<String, Object>();
            resultMap.put("lanshanUserId", currentIdentifyDto.getLanshanUserId());
            resultMap.put("currentIdentify", identifyApiService.getCurrentIdentify(currentIdentifyDto));
            return Result.build(resultMap);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(30).setMsg(e.getMessage());
        }
    }

    @ApiCallLog(appType = AppTypeEnum.INTERNAL)
    @ApiOperation(value = "更换当前身份")
    @PostMapping(value = "changeCurrentIdentify")
    public Result<Object> changeCurrentIdentify(@Validated ChangeCurrentIdentifyDto changeCurrentIdentifyDto) {
        try {
            if (StringUtils.isEmpty(changeCurrentIdentifyDto.getLanshanUserId())) {
                throw new ServiceException("览山用户id不能为空", 41);
            }
            if (changeCurrentIdentifyDto.getIdentifyId() == null) {
                throw new ServiceException("新身份id不能为空", 42);
            }
            identifyApiService.changeCurrentIdentify(changeCurrentIdentifyDto);
            return Result.build("成功", 0);
        } catch (ServiceException se) {
            return Result.build().setCode(se.getCode()).setMsg(se.getMessage());
        } catch (Exception e) {
            return Result.build().setCode(40).setMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "发送消息")
    @PostMapping(value = "sendTest")
    public Result<Boolean> sendMsg() {
        producer.sendMessage("test:..........................");
        return Result.build(true);
    }

    @ApiOperation(value = "测试新增强消息发送功能")
    @PostMapping(value = "sendEnhancedTest")
    public Result<Boolean> sendEnhancedMsg() {
        // 测试各种类型的消息发送
        Map<String, Object> testData = new HashMap<>();
        testData.put("message", "Enhanced MQ Test");
        testData.put("timestamp", System.currentTimeMillis());
        
        // 测试Direct交换机
        enhancedMqMessageSender.sendToDirectExchange("test.exchange", "test.routing.key", testData);
        
        // 测试优先级消息
        enhancedMqMessageSender.sendMessageWithPriority("test.exchange", "test.routing.key", testData, 5);
        
        return Result.build(true);
    }

}
