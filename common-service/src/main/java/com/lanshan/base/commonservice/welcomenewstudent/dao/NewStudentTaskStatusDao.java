package com.lanshan.base.commonservice.welcomenewstudent.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTaskStatus;

/**
 * 新生任务状态表(NewStudentTaskStatus)表数据库访问层
 *
 * <AUTHOR>
 */
public interface NewStudentTaskStatusDao extends BaseMapper<NewStudentTaskStatus> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentTaskStatus> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<NewStudentTaskStatus> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentTaskStatus> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<NewStudentTaskStatus> entities);

}

