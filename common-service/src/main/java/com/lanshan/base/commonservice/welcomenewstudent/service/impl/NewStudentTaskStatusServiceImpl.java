package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskStatusDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTaskStatus;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTaskStatusService;
import org.springframework.stereotype.Service;

/**
 * 新生任务状态表(NewStudentTaskStatus)表服务实现类
 *
 * <AUTHOR>
 */
@Service("newStudentTaskStatusService")
public class NewStudentTaskStatusServiceImpl extends ServiceImpl<NewStudentTaskStatusDao, NewStudentTaskStatus> implements NewStudentTaskStatusService {

}

