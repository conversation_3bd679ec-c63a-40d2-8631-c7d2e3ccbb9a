package com.lanshan.base.commonservice.addressbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (NewStuMsgSendStatus)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-21 14:28:57
 */
public interface NewStuMsgSendStatusMapper extends BaseMapper<NewStuMsgSendStatusEntity> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<NewStuMsgSendStatusEntity> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<NewStuMsgSendStatusEntity> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<NewStuMsgSendStatusEntity> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<NewStuMsgSendStatusEntity> entities);

    NewStuMsgSendStatusEntity selectByUserId(@Param("userId") String userId);

    void insertByme(@Param("entity") NewStuMsgSendStatusEntity entity);

    void updateByMe(@Param("entity") NewStuMsgSendStatusEntity entity);
}

