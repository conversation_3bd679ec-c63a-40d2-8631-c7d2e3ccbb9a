package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentGroupDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import org.springframework.stereotype.Service;

/**
 * 新生群表(NewStudentGroup)表服务实现类
 *
 * <AUTHOR>
 */
@Service("newStudentGroupService")
public class NewStudentGroupServiceImpl extends ServiceImpl<NewStudentGroupDao, NewStudentGroup> implements NewStudentGroupService {

}

