package com.lanshan.base.commonservice.welcomenewstudent.converter;


import java.util.List;

import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentDataVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 新生信息表(NewStudentData)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewStudentDataConverter {

    NewStudentDataConverter INSTANCE = Mappers.getMapper(NewStudentDataConverter.class);

    NewStudentDataVO toVO(NewStudentData entity);

    NewStudentData toEntity(NewStudentDataVO vo);

    List<NewStudentDataVO> toVO(List<NewStudentData> entityList);

    List<NewStudentData> toEntity(List<NewStudentDataVO> voList);
}


