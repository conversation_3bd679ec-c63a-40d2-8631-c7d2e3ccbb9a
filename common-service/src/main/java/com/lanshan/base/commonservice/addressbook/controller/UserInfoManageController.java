package com.lanshan.base.commonservice.addressbook.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.converter.UserInfoConverter;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 用户信息列表(UserInfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("user-info/manage/")
@Api(tags = "用户信息列表(UserInfo)控制层", hidden = true)
public class UserInfoManageController {
    /**
     * 服务对象
     */
    @Resource
    private UserInfoService userInfoService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<UserInfoVO>> selectAll(Page<UserInfoVO> page, UserInfoVO vo) {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>(UserInfoConverter.INSTANCE.toEntity(vo));
        IPage<UserInfo> pageData = this.userInfoService.page(page.convert(UserInfoConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(UserInfoConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<UserInfoVO> selectOne(@PathVariable Serializable id) {
        return Result.build(UserInfoConverter.INSTANCE.toVO(this.userInfoService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping
    public Result<Boolean> insert(@RequestBody UserInfoVO vo) {
        return Result.build(this.userInfoService.save(UserInfoConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改数据")
    public Result<Boolean> update(@RequestBody UserInfoVO vo) {
        return Result.build(this.userInfoService.updateById(UserInfoConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.userInfoService.removeByIds(idList));
    }

    @ApiOperation("导入新生数据")
    @PostMapping("importNewStudent")
    public Result<Void> importNewStudent(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        userInfoService.importNewStudent(file, response);
        return Result.build();
    }
}

