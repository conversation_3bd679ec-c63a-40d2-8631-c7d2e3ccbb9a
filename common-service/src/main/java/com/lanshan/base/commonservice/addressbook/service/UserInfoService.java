package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.excel.UserNewStuImportDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息列表(UserInfo)表服务接口
 *
 * <AUTHOR>
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 通过ID查询单条数据
     *
     * @param idCard 加密后的身份证号
     * @return 实例对象
     */
    List<UserInfo> getUserInfoByIdCard(String idCard);

    /**
     * 导入新生数据
     *
     * @param file     文件
     * @param response
     */
    void importNewStudent(MultipartFile file, HttpServletResponse response);

    /**
     * 获取导入新生数据错误数据
     *
     * @return 错误数据
     */
    List<UserNewStuImportDTO> getImportNewStudentErrorData();
}

