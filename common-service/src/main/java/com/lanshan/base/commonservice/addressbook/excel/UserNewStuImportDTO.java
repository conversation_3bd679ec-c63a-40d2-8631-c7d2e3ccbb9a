package com.lanshan.base.commonservice.addressbook.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.lanshan.base.commonservice.enums.IdentityTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class UserNewStuImportDTO {

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("学工号")
    private String userid;

    @ExcelProperty(value = "证件类型", converter = IdentityTypeConverter.class)
    private String identityType;

    @ExcelProperty("证件号码")
    private String idCardNum;

    @ExcelProperty("手机号")
    private String phoneNo;

    @ExcelProperty(value = "用户身份类型 4 是新生")
    private String userType;

    @ExcelProperty("部门编码")
    private String deptCode;

    @ExcelProperty("部门名称")
    private String deptName;

    @ExcelProperty("错误信息")
    private String errorMsg;

    static class IdentityTypeConverter implements Converter<String> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return String.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            return IdentityTypeEnum.getValueByRemark(cellData.getStringValue());
        }

        @Override
        public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String remarkByValue = IdentityTypeEnum.getRemarkByValue((value));
            return remarkByValue == null ? new WriteCellData<>("-") : new WriteCellData<>(remarkByValue);
        }
    }
}
