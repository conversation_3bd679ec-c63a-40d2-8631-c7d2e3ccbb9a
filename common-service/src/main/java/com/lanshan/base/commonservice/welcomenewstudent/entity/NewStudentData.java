package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * 新生信息表(NewStudentData)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentData extends Model<NewStudentData> {
    /**
     * 身份证号
     */
    private String idCardNum;
    /**
     * 姓名
     */
    private String name;
    /**
     * 学号
     */
    private String userid;
    /**
     * 手机号
     */
    private String phoneNo;
    /**
     * 入学年份 例如 2025
     */
    private String year;
    /**
     * 院系代码
     */
    private String deptCode;
    /**
     * 院系名称
     */
    private String deptName;
    /**
     * 是否认证
     */
    private Boolean authStatus;
    /**
     * 是否开通
     */
    private Boolean openStatus;
    /**
     * 是否开始发送消息状态
     */
    private Boolean startSendStatus;
    /**
     * 是否结束发送消息状态
     */
    private Boolean endSendStatus;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.idCardNum;
    }
}

