package com.lanshan.base.commonservice.welcomenewstudent.entity;

import java.util.Date;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;

import lombok.ToString;


/**
 * 新生群表(NewStudentGroup)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentGroup extends Model<NewStudentGroup> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 群聊id
     */
    private String chatId;
    /**
     * 群名称
     */
    private String groupName;
    /**
     * 群主id
     */
    private String ownerUserid;
    /**
     * 群主名称
     */
    private String ownerName;
    /**
     * 加入群的用户列表
     */
    private String joinInUser;
    /**
     * 院系代码
     */
    private String deptCode;
    /**
     * 院系名称
     */
    private String deptName;
    /**
     * 入学年份
     */
    private String year;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

