package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.api.dto.user.UserDeptIdsTagIdsDto;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.entity.CpUserBak;
import com.lanshan.base.commonservice.addressbook.entity.CpUserDepartmentRelationOperate;
import com.lanshan.base.commonservice.addressbook.entity.CpUserOperate;
import com.lanshan.base.commonservice.task.batch.vo.JobExecVo;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

/**
 * 用户表(User)服务接口
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface UserService extends IService<CpUser> {

    /**
     * 异步方式同步用户操作至企微
     *
     * @param corpId
     * @param userOperateList
     * @param userMap
     * @param toInvite
     * @return
     */
    Future<Integer> asyncUserOperateToCp(String corpId, List<CpUserOperate> userOperateList, Map<String, CpUser> userMap, boolean toInvite);

    /**
     * 异步方式同步用户备份至企微
     *
     * @param corpId
     * @param userOperateList
     * @return
     */
    Future<Integer> asyncUserBakToCp(String corpId, List<CpUserBak> userOperateList);

    /**
     * 异步方式更新用户至企微
     *
     * @param corpId
     * @param userList
     * @return
     */
    Future<Integer> asyncUpdateUserDeptToCp(String corpId, List<CpUser> userList);

    /**
     * 根据用户id列表获取成员用户列表
     *
     * @param useridList
     * @return
     */
    List<UserInfoVo> listUsersByUseridList(List<String> useridList);

    /**
     * 根据用户id列表和用户状态获取成员用户列表
     *
     * @param useridList
     * @return
     */
    List<UserInfoVo> listUsersByUseridAndStatusList(List<String> useridList);

    /**
     * 获取部门成员(仅当前部门)
     *
     * @param departmentid
     * @return
     */
    List<UserInfoVo> listUsersByDepartmentid(Long departmentid);

    /**
     * 获取部门成员（包含当前部门及其下全部子部门，路径匹配查询）
     *
     * @param departmentid
     * @return
     */
    List<UserInfoVo> listAllUsersByDepartmentid(Long departmentid);

    /**
     * 获取多个部门成员（包含当前部门及其下全部子部门，路径匹配查询）
     *
     * @param departmentids
     * @return
     */
    List<UserInfoVo> listAllUsersByDepartmentids(List<Long> departmentids);


    /**
     * 根据标签idList查询标签下的用户
     *
     * @param tagIds 标签ids
     * @return 标签列表
     */
    List<UserInfoVo> listAllUserByTagList(List<Long> tagIds);

    /**
     * 根据成员、部门、标签id信息获取有效成员信息列表
     *
     * @param userInfoQo
     * @return
     */
    Set<UserInfoVo> getUsersScope(UserInfoQo userInfoQo);

    /**
     * 分页获取用户详情信息（用于完整信息查询搜索）
     *
     * @param userInfoPageQo
     * @return
     */
    Page<UserInfoVo> pageUserInfo(UserInfoPageQo userInfoPageQo);

    /**
     * 获取用户总数（用于完整信息查询搜索）
     *
     * @param userInfoPageQo
     * @return
     */
    Integer getUserInfoCount(UserInfoPageQo userInfoPageQo);

    /**
     * 校验用户是否存在
     *
     * @param userIds
     * @return
     */
    List<String> listNotExistUser(List<String> userIds);

    /**
     * 修改企微通讯录成员的手机号
     *
     * @param qo
     */
    void changeMobile(ChangeMobileQo qo);

    /**
     * 异步方式同步用户部门关系至企微
     *
     * @param corpId
     * @param userList
     * @param userDeptMap
     * @return
     */
    Future<Integer> asyncUserDeptRelationToCp(String corpId, List<UserSaveQo> userList, Map<String, List<CpUserDepartmentRelationOperate>> userDeptMap);

    /**
     * 根据用户id获取部门ids、标签ids
     * （标签ID包括用户所在部门及所有上级部门关联的标签）
     *
     * @param userid 用户id
     */
    UserDeptIdsTagIdsDto getDeptIdsAndTagIdsByUserId(String userid);

    /**
     * 根据企微消息保存用户
     *
     * @param wxMessage 企业微信消息
     */
    void saveUserByMsg(WxCpXmlMessage wxMessage);

    /**
     * 根据企微消息删除用户
     *
     * @param wxMessage 企业微信消息
     */
    void delUserByMsg(WxCpXmlMessage wxMessage);

    /**
     * 新增用户（包含标签）
     *
     * @param qo 用户信息
     */
    String saveUserWithTag(UserSaveWithTagQo qo);

    /**
     * 批量新增用户部门
     *
     * @param qo 用户信息
     */
    void batchUserSaveDept(BatchUserDeptQo qo);

    /**
     * 批量更新用户部门
     *
     * @param qo 用户信息
     */
    void batchUserUpdateDept(BatchUserDeptQo qo);

    /**
     * 导入通讯录
     *
     * @param file     文件
     * @param response 响应
     */
    void importUser(MultipartFile file, HttpServletResponse response) throws IOException;

    /**
     * 导出通讯录
     *
     * @param userInfoPageQo 查询条件
     * @param response       响应
     */
    void exportUser(UserInfoPageQo userInfoPageQo, HttpServletResponse response) throws IOException;

    /**
     * 下载导入通讯录模板
     */
    void downloadImportUserTemplate(HttpServletResponse response);

    /**
     * 批量禁用用户
     *
     * @param qo 用户参数
     */
    void batchEnableOrForbidUser(BatchEnableOrdForbidUserQo qo);

    /**
     * 批量删除用户
     *
     * @param useridList 用户id列表
     */
    void batchDelUser(List<String> useridList);

    /**
     * 根据id列表获取用户列表
     *
     * @param ids id列表
     * @return 用户列表
     */
    List<CpUser> listByIds(List<String> ids);

    /**
     * 批量邀请成员
     *
     * @param qo 邀请信息
     */
    void batchInvite(BatchInviteQo qo);

    /**
     * 根据姓名与手机号查询用户
     *
     * @param name   姓名
     * @param mobile 手机号
     * @return 用户信息
     */
    UserInfoVo getUserByNameAndMobile(String name, String mobile);

    /**
     * 根据姓名查询用户  访客预约小程序使用 通讯录获取不到手机号，所以无法验证。此处改成只根据姓名查询，仅用于访客预约小程序演示使用
     * @param name
     * @return
     */
     UserInfoVo getUserByName(String name );

    /**
     * 获取全量用户缓存
     */
    CpUser getCacheUserByUserid(String userid);


    /**
     * 获取全量用户缓存
     */
    List<CpUser> listCacheUserByUserids(Collection<String> userid);

    /**
     * 根据userid获取用户信息
     *
     * @param userid 用户id
     * @return 用户信息
     */
    UserInfoVo getUserByUserid(String userid);

    /**
     * 二次验证
     *
     * @param userid 用户 userid
     */
    void authSucc(String userid);

    /**
     * 批量删除企微用户
     * @param useridList 用户id列表
     */
    void batchDelCpUser(List<String> useridList);

    /**
     * 获取所有用户id（不包含excludeUserids）
     *
     * @param pageQo 需要排除的用户id列表
     * @return 所有用户id列表
     */
    Set<String> selectAllUserid(UserInfoPageQo pageQo);


    /**
     * 批量更新用户部门 V2
     *
     * @param qo 用户信息
     */
    void batchUserUpdateDeptV2(BatchUserDeptQo qo);

    /**
     * 批量禁用用户V2
     *
     * @param qo 用户参数
     */
    void batchEnableOrForbidUserV2(BatchEnableOrdForbidUserQo qo);

    /**
     * 批量新增用户部门V2
     *
     * @param qo 用户信息
     */
    void batchUserSaveDeptV2(BatchUserDeptQo qo);

    /**
     * 批量删除用户V2
     *
     * @param useridList 用户id列表
     */
    void batchDelUserV2(@NotEmpty(message = "参数列表不能为空") List<String> useridList);

    /*
     * <p>异步框架-删除用户</p>
     *
     * @param useridList
     * <AUTHOR>
     * @date 2024/12/11 上午11:14
     */
    void batchDelUserSync(List<String> useridList);

    /*
     * <p>批量执行后，异步数据回显</p>
     *
     * @param item
     * <AUTHOR>
     * @date 2024/12/11 上午11:03
     */
    void updateUser(JobExecVo item);
    void deleteUser(JobExecVo item);

    /**
     * 批量删除部门用户
     *
     * @param deptId 部门id
     */
    void batchDelDeptUser(Long deptId);

    /**
     * 提交用户信息并开通企微
     *
     * @param qo 用户信息
     * @return
     */
    String submitUserInfo(SubmitUserInfoQo qo);

    /**
     * 获取所有已激活的身份为学生的用户
     * <AUTHOR> yang.
     * @since 2025/4/28 11:34
     */
    Collection<UserInfoPartVO> getActiveUsers();

    /**
     * 批量获取部门成员(仅当前部门)
     * <AUTHOR> yang.
     * @since 2025/5/7 19:40
     */
    List<UserInfoPartVO> listUsersByDeptIds(List<Long> deptIds);
}
