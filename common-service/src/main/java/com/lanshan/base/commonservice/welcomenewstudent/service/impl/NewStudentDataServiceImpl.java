package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentDataDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import org.springframework.stereotype.Service;

/**
 * 新生信息表(NewStudentData)表服务实现类
 *
 * <AUTHOR>
 */
@Service("newStudentDataService")
public class NewStudentDataServiceImpl extends ServiceImpl<NewStudentDataDao, NewStudentData> implements NewStudentDataService {

}

