package com.lanshan.base.commonservice.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpChatService;
import me.chanjar.weixin.cp.bean.WxCpChat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
public class ProvisionalNewStuGroup {

    @Resource
    private NewStudentGroupService newStudentGroupService;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @Resource
    private UserService userService;

    @Value("${newStu.corpId:}")
    private String corpId;
    @Value("${newStu.agentId:}")
    private String agentId;

    @XxlJob("createNewStuGroup")
    public void createNewStuGroup() {
        List<UserInfo> newStuUsers = userInfoService.list(Wrappers.lambdaQuery(UserInfo.class).eq(UserInfo::getUserType, "4"));
        if (CollUtil.isEmpty(newStuUsers)) {
            log.info("没有新生需要拉入群聊");
            return;
        }
        List<String> userIds = newStuUsers.stream().map(UserInfo::getUserId).collect(Collectors.toList());
        List<UserInfoVo> userInfoVos = userService.listUsersByUseridList(userIds);
        if (CollUtil.isEmpty(userInfoVos)) {
            log.info("没有找到已加入企微的新生信息");
            return;
        }
        List<NewStudentGroup> newStudentGroupList = newStudentGroupService.list(Wrappers.lambdaQuery(NewStudentGroup.class).isNotNull(NewStudentGroup::getChatId));
        if (CollUtil.isEmpty(newStudentGroupList)) {
            log.info("没有需要拉新生的群");
            return;
        }
        Map<String, NewStudentGroup> groupMap = newStudentGroupList.stream().collect(Collectors.toMap(NewStudentGroup::getDeptName, newStudentGroupEntity -> newStudentGroupEntity, (o, n) -> o));

        newStuUsers.stream().filter(newStuUser -> userInfoVos.stream().anyMatch(userInfoVo -> userInfoVo.getUserid().equals(newStuUser.getUserId())))
                .forEach(newStuUser -> {
                    String deptName = newStuUser.getDeptName();
                    NewStudentGroup newStudentGroup = groupMap.get(deptName);
                    if (newStudentGroup != null) {
                        try {
                            WxCpChatService chatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
                            WxCpChat wxCpChat = chatService.get(newStudentGroup.getChatId());
                            chatService.update(wxCpChat.getId(), wxCpChat.getName(), null, CollUtil.newArrayList(newStuUser.getUserId()), null);
                        } catch (WxErrorException e) {
                            log.error("请求企微异常：", e);
                        }
                    }
                });
    }
}
