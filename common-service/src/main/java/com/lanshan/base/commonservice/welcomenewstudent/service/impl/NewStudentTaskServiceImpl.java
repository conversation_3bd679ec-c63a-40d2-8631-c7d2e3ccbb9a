package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTaskService;
import org.springframework.stereotype.Service;

/**
 * 新生任务表(NewStudentTask)表服务实现类
 *
 * <AUTHOR>
 */
@Service("newStudentTaskService")
public class NewStudentTaskServiceImpl extends ServiceImpl<NewStudentTaskDao, NewStudentTask> implements NewStudentTaskService {

}

