package com.lanshan.base.commonservice.welcomenewstudent.converter;


import java.util.List;

import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTaskStatus;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentTaskStatusVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 新生任务状态表(NewStudentTaskStatus)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewStudentTaskStatusConverter {

    NewStudentTaskStatusConverter INSTANCE = Mappers.getMapper(NewStudentTaskStatusConverter.class);

    NewStudentTaskStatusVO toVO(NewStudentTaskStatus entity);

    NewStudentTaskStatus toEntity(NewStudentTaskStatusVO vo);

    List<NewStudentTaskStatusVO> toVO(List<NewStudentTaskStatus> entityList);

    List<NewStudentTaskStatus> toEntity(List<NewStudentTaskStatusVO> voList);
}


