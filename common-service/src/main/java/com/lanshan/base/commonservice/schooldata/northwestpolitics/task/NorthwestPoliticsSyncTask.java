package com.lanshan.base.commonservice.schooldata.northwestpolitics.task;

import cn.hutool.core.date.DateUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdDwxxService;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdJzgxxService;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdXsxjxxService;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdXsxxService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 西北政法大学数据同步定时任务
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */


@Slf4j
@Component
public class NorthwestPoliticsSyncTask {

    @Resource
    private SdDwxxService sdDwxxService;
    @Resource
    private SdJzgxxService sdJzgxxService;
    @Resource
    private SdXsxjxxService sdXsxjxxService;
    @Resource
    private SdXsxxService sdXsxxService;

    @XxlJob("northwestPoliticsSyncData")
    public void northwestPoliticsSyncData() {
        long startSeconds = DateUtil.currentSeconds();
        log.info("准备开始同步西北政法大学数据");
        sdDwxxService.syncSdDwxx();
        sdJzgxxService.syncSdJzgxx();
        sdXsxjxxService.syncSdXsxjxx();
        sdXsxxService.syncSdXsx();
        long endSeconds = DateUtil.currentSeconds();
        log.info("同步西北政法大学数据完成, 耗时: {} 秒", endSeconds - startSeconds);
    }

}
