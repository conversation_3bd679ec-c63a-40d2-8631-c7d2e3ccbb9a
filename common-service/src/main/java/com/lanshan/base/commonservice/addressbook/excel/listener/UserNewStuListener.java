package com.lanshan.base.commonservice.addressbook.excel.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.lanshan.base.api.enums.UserTypeEnum;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.excel.UserNewStuImportDTO;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import com.lanshan.base.commonservice.common.utils.AesUtil;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 新生导入监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class UserNewStuListener implements ReadListener<UserNewStuImportDTO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 1000;

    private final UserInfoService userInfoService;

    private final RedissonClient redissonClient;

    private final List<UserNewStuImportDTO> errorImportDataList = new ArrayList<>(BATCH_COUNT);

    private final LongAdder successAdder = new LongAdder();

    /**
     * 缓存的数据
     */
    private final List<UserNewStuImportDTO> cachedDataList = new ArrayList<>(BATCH_COUNT * 2);

    public UserNewStuListener(UserInfoService userInfoService, RedissonClient redissonClient) {
        this.userInfoService = userInfoService;
        this.redissonClient = redissonClient;
    }

    @Override
    public void invoke(UserNewStuImportDTO data, AnalysisContext context) {
        //校验数据完整性
        if (checkParam(data)) {
            return;
        }
        //将数据缓存到集合中
        cachedDataList.add(data);
        //当达到BATCH_COUNT时，进行存储并清空当前缓存数据
        if (cachedDataList.size() >= BATCH_COUNT) {
            doSaveUserInfo();
            cachedDataList.clear();
        }
        successAdder.increment();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！,成功条数：{},失败条数：{}", successAdder.longValue(), errorImportDataList.size());
        //保存错误人员数据到缓存中
        redissonClient.getList(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST).addAll(errorImportDataList);
    }

    private boolean checkParam(UserNewStuImportDTO data) {
        String errorMsg = "";
        if (StrUtil.isBlank(data.getName())) {
            errorMsg = errorMsg + "姓名不能为空；";
        }
        if (StrUtil.isBlank(data.getIdentityType())) {
            errorMsg = errorMsg + "证件类型不能为空；";
        }
        if (StrUtil.isBlank(data.getIdCardNum())) {
            errorMsg = errorMsg + "证件号码不能为空；";
        }
        if (StrUtil.isBlank(data.getUserType())) {
            errorMsg = errorMsg + "用户类型不能为空；";
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            data.setErrorMsg(errorMsg);
            errorImportDataList.add(data);
            return false;
        }
        return true;
    }

    /**
     * 批量保存
     */
    private void doSaveUserInfo() {
        final List<NewStudentData> newStudentDataList = new ArrayList<>(BATCH_COUNT);

        List<UserInfo> userInfoList = cachedDataList.stream().map(data -> {
            UserInfo userInfo = new UserInfo();
            String userid = data.getUserid();
            if (StrUtil.isBlank(userid)) {
                userid = data.getIdCardNum();
            }
            userInfo.setUserId(userid);
            userInfo.setUserName(data.getName());
            userInfo.setIdentityNumber(AesUtil.AESEncrypt(data.getIdCardNum()));
            userInfo.setIdentityType(data.getIdentityType());
            userInfo.setPhoneNo(data.getPhoneNo());
            userInfo.setDeptCode(data.getDeptCode());
            userInfo.setDeptName(data.getDeptName());
            userInfo.setCreateDate(new Date());
            userInfo.setUpdateDate(new Date());
            userInfo.setIdentityNumberWrapper(DesensitizedUtil.idCardNum(data.getIdCardNum(), 5, 2));
            userInfo.setUserType(UserTypeEnum.getCodeStrByDesc(data.getUserType()));
            userInfo.setEncrypted(true);
            NewStudentData newStudentData = new NewStudentData();
            BeanUtils.copyProperties(data, newStudentData);
            newStudentData.setYear(String.valueOf(DateUtil.thisYear()));

            return userInfo;
        }).collect(Collectors.toList());
        userInfoService.saveBatch(userInfoList, BATCH_COUNT);
    }
}
