package com.lanshan.base.commonservice.welcomenewstudent.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentGroupConverter;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 新生群表(NewStudentGroup)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("newStudentGroup")
@Api(tags = "新生群表(NewStudentGroup)控制层", hidden = true)
public class NewStudentGroupController {
    /**
     * 服务对象
     */
    @Resource
    private NewStudentGroupService newStudentGroupService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<NewStudentGroupVO>> selectAll(Page<NewStudentGroupVO> page, NewStudentGroupVO vo) {
        QueryWrapper<NewStudentGroup> queryWrapper = new QueryWrapper<>(NewStudentGroupConverter.INSTANCE.toEntity(vo));
        IPage<NewStudentGroup> pageData = this.newStudentGroupService.page(page.convert(NewStudentGroupConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(NewStudentGroupConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<NewStudentGroupVO> selectOne(@PathVariable Serializable id) {
        return Result.build(NewStudentGroupConverter.INSTANCE.toVO(this.newStudentGroupService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody NewStudentGroupVO vo) {
        return Result.build(this.newStudentGroupService.save(NewStudentGroupConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody NewStudentGroupVO vo) {
        return Result.build(this.newStudentGroupService.updateById(NewStudentGroupConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.newStudentGroupService.removeByIds(idList));
    }
}

