package com.lanshan.base.commonservice.task;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.api.dto.message.NewArticleBody;
import com.lanshan.base.api.dto.message.NewsMsgBody;
import com.lanshan.base.api.enums.MsgChannelEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.mapper.CpUserMapper;
import com.lanshan.base.commonservice.message.handler.ChannelHandlerFactory;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @createTime: 2025-05-28 09:37
 */
@Slf4j
@Component
public class NewStuTask {
    @Resource
    private CpUserMapper cpUserMapper;
    @Value("${newStu.corpId}")
    private String corpId;
    @Value("${newStu.agentId}")
    private String agentId;
    @Value("${newStu.msgPic}")
    private String msgPic;
    @Value("${newStu.msgUrl}")
    private String msgUrl;
    @Resource
    private ChannelHandlerFactory channelHandlerFactory;

    @Resource
    NewStuMsgSendStatusMapper newStuMsgSendStatusMapper;

    /**
     * 发送图文消息
     */
    @XxlJob("sendPicMsg")
    public void sendPicMsg() {
        try {
            List<CpUser> newStudentList = cpUserMapper.getActiveNewStudent();
//            String userIds = newStudentList.stream()
//                    .map(CpUser::getUserid) // 提取userId字段
//                    .collect(Collectors.joining("|"));
//            if(StringUtils.isNotEmpty(userIds)) {
//                log.info("send users : {} ",userIds);
//                NewsMsgBody newsMsgBody = new NewsMsgBody();
//                newsMsgBody.setMsgType(MsgTypeEnum.NEWS.value());
//                newsMsgBody.setAgentId(Integer.valueOf(agentId));
//                newsMsgBody.setToUser(userIds);
//                NewArticleBody newArticleBody = new NewArticleBody();
//                newArticleBody.setTitle("欢迎新同学！请查收您的新生任务");
//                newArticleBody.setUrl(msgUrl);
//                newArticleBody.setPicUrl(msgPic);
//                newsMsgBody.setArticles(Collections.singletonList(newArticleBody));
//                Result<Object> newsResult = channelHandlerFactory.getChannelHandler(MsgChannelEnum.WEIXIN_CORP_CHANNEL).handle(newsMsgBody);
//                if (newsResult.hasError()) {
//                    log.error("消息中心发送企微图文消息失败，错误码：{}，失败信息：{}", newsResult.getCode(), newsResult.getMsg());
//                }
//                log.info("消息中心发送企微图文消息success:{}",userIds);
//            }
            if (CollUtil.isNotEmpty(newStudentList)) {
                for (CpUser cp : newStudentList) {
                    String userId = cp.getUserid();
                    if(StringUtils.isEmpty(userId)){
                        continue;
                    }
                    log.info("send users : {} ", userId);
                    NewStuMsgSendStatusEntity entity = newStuMsgSendStatusMapper.selectByUserId(userId);
                    if(entity!=null && entity.getActiveMsgStatus()==1){
                        log.info("无需发送消息: {} ", userId);
                        continue;
                    }

                    NewsMsgBody newsMsgBody = new NewsMsgBody();
                    newsMsgBody.setMsgType(MsgTypeEnum.NEWS.value());
                    newsMsgBody.setAgentId(Integer.valueOf(agentId));
                    newsMsgBody.setToUser(userId);
                    NewArticleBody newArticleBody = new NewArticleBody();
                    newArticleBody.setTitle("欢迎新同学！请查收您的新生任务");
                    String jumpUrl = msgUrl + "?scope=snsapi_base&corpId=" + corpId + "&agentId=" + agentId + "&newStuUserId=" + userId;
                    newArticleBody.setUrl(jumpUrl);
                    newArticleBody.setPicUrl(msgPic);
                    newsMsgBody.setArticles(Collections.singletonList(newArticleBody));
                    Result<Object> newsResult = channelHandlerFactory.getChannelHandler(MsgChannelEnum.WEIXIN_CORP_CHANNEL).handle(newsMsgBody);
                    if (newsResult.hasError()) {
                        log.error("消息中心发送企微图文消息失败，错误码：{}，失败信息：{}", newsResult.getCode(), newsResult.getMsg());
                    }
                    if(entity==null) {
                        entity = new NewStuMsgSendStatusEntity();
                        entity.setNewTaskMsg(0);
                        entity.setActiveMsgStatus(1);
                        entity.setUserId(userId);
                        newStuMsgSendStatusMapper.insertByme(entity);
                    }else{
                        entity.setActiveMsgStatus(1);
                        newStuMsgSendStatusMapper.updateByMe(entity);
                    }
                }
            }
        } catch (Exception e) {
            log.error("megsend - error ", e);
        }
    }

}
