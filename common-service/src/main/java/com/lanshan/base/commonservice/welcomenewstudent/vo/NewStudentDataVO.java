package com.lanshan.base.commonservice.welcomenewstudent.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.io.Serializable;

/**
 * 新生信息表(NewStudentData)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新生信息表VO")
@Data
@ToString
public class NewStudentDataVO implements Serializable {

    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "学号")
    private String userid;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "入学年份 例如 202509")
    private String year;

    @ApiModelProperty(value = "院系代码")
    private String deptCode;

    @ApiModelProperty(value = "院系名称")
    private String deptName;

    @ApiModelProperty(value = "是否认证")
    private Boolean authStatus;

    @ApiModelProperty(value = "是否开通")
    private Boolean openStatus;

    @ApiModelProperty(value = "是否开始发送消息状态")
    private Boolean startSendStatus;

    @ApiModelProperty(value = "是否结束发送消息状态")
    private Boolean endSendStatus;
}

