package com.lanshan.base.commonservice.welcomenewstudent.converter;


import java.util.List;

import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 新生群表(NewStudentGroup)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewStudentGroupConverter {

    NewStudentGroupConverter INSTANCE = Mappers.getMapper(NewStudentGroupConverter.class);

    NewStudentGroupVO toVO(NewStudentGroup entity);

    NewStudentGroup toEntity(NewStudentGroupVO vo);

    List<NewStudentGroupVO> toVO(List<NewStudentGroup> entityList);

    List<NewStudentGroup> toEntity(List<NewStudentGroupVO> voList);
}


