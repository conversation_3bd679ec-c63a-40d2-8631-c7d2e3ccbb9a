package com.lanshan.base.commonservice.addressbook.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.group.dto.UpdateGroupOwnerDto;
import com.lanshan.base.commonservice.group.qo.SendGroupChatMessageQo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * @program: wx-demo
 * @Description:
 * @createTime: 2025-05-20 15:16
 */
@RestController
@RequestMapping("newStudentGroup")
@Api(tags = "新生群管理控制层")
public class StuGroupController {

    @Autowired
    StuGroupService stuGroupService;

    @ApiOperation("导入群聊数据")
    @PostMapping("importGroup")
    public Result<Object> importGroup( @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.build().setCode(500).setMsg("请选择要上传的Excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            return Result.build().setCode(500).setMsg("请上传Excel格式的文件(.xls或.xlsx)");
        }
        try {
            return Result.build(stuGroupService.importGroupData(file));
        }catch (Exception e){
            return Result.build().setCode(500).setMsg(e.getMessage());
        }
    }
    @PostMapping("/updateGroupOwner")
    @ApiOperation("修改群主")
    public Result<Void> updateGroupOwner(@RequestBody UpdateGroupOwnerDto dto) throws WxErrorException {
        this.stuGroupService.updateGroupOwner(dto);
        return Result.build();
    }

    @PostMapping("/sendGroupMsg")
    @ApiOperation("发送群消息")
    public Result<Object> sendGroupMsg(SendGroupChatMessageQo vo) throws WxErrorException {
        this.stuGroupService.sendGroupMsg(vo);
        return Result.build();
    }

    @PostMapping("/sendTaskMsg")
    @ApiOperation("新生任务消息发送")
    public Result<Object> sendTaskMsg(@RequestParam(name = "userId") String userId) throws WxErrorException {
        return Result.build(this.stuGroupService.sendTaskMsg(userId));
    }
    @ApiOperation("导入新生数据")
    @PostMapping("importNewStudent")
    public Result<Object> importNewStudent( @RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.build().setCode(500).setMsg("请选择要上传的Excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null || !(fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            return Result.build().setCode(500).setMsg("请上传Excel格式的文件(.xls或.xlsx)");
        }
        try {
            return Result.build(stuGroupService.importNewStudentData(file));
        }catch (Exception e){
            return Result.build().setCode(500).setMsg(e.getMessage());
        }
    }
}
