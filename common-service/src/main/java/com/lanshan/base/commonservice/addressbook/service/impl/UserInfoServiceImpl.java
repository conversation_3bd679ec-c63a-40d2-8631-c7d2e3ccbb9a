package com.lanshan.base.commonservice.addressbook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 用户信息列表(UserInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoService")
public class UserInfoServiceImpl extends ServiceImpl<UserInfoDao, UserInfo> implements UserInfoService {

    /**
     * @param idCard 身份证号（密文）
     * @return 用户信息列表
     */
    @Override
    public List<UserInfo> getUserInfoByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return Collections.emptyList();
        }
        return super.list(new LambdaQueryWrapper<UserInfo>()
                .eq(UserInfo::getIdentityNumber, idCard));
    }

    @Override
    public void importNewStudent(MultipartFile file) {
        if (file.isEmpty()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请选择要上传的Excel文件");
        }
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        // 获取当前年份
        final int currentYear = LocalDate.now().getYear();
        String userId = SecurityContextHolder.getUserId();
        final String opUserName = SecurityContextHolder.getUserName();
        try (InputStream is = file.getInputStream()) {

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

