package com.lanshan.base.commonservice.addressbook.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.excel.UserNewStuImportDTO;
import com.lanshan.base.commonservice.addressbook.excel.listener.UserNewStuListener;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

/**
 * 用户信息列表(UserInfo)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoService")
public class UserInfoServiceImpl extends ServiceImpl<UserInfoDao, UserInfo> implements UserInfoService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private NewStudentDataService newStudentDataService;

    /**
     * @param idCard 身份证号（密文）
     * @return 用户信息列表
     */
    @Override
    public List<UserInfo> getUserInfoByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return Collections.emptyList();
        }
        return super.list(new LambdaQueryWrapper<UserInfo>()
                .eq(UserInfo::getIdentityNumber, idCard));
    }

    @Override
    public void importNewStudent(MultipartFile file, HttpServletResponse response) {
        if (file == null || file.isEmpty()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请选择要上传的Excel文件");
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !(originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx"))) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请上传Excel格式的文件(.xls或.xlsx)");
        }
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        String userId = SecurityContextHolder.getUserId();
        final String opUserName = SecurityContextHolder.getUserName();
        try (InputStream is = file.getInputStream()) {
            EasyExcel.read(is)
                    .head(UserNewStuImportDTO.class)
                    .sheet()
                    .headRowNumber(1)
                    .registerReadListener(new UserNewStuListener(userId, opUserName, this, redissonClient, newStudentDataService))
                    .doRead();
        } catch (IOException e) {
            log.error("导入新生数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入新生数据异常");
        }
    }
}

