package com.lanshan.base.commonservice.config.rabbitmq;

/**
 * MQ常量定义
 * 统一管理所有RabbitMQ相关常量
 */
public interface MqConstant {
    // === 身份相关常量 ===
    String IDENTIFY_QUEUE = "identify.queue";
    String IDENTIFY_EXCHANGE = "identify.exchange";
    String IDENTIFY_ROUTER_KEY = "identify.routing.key";
    String IDENTIFY_CHANGE_ROUTER_KEY = "identify.change.routing.key";
    
    // === 新增通用交换机类型常量 ===
    String DIRECT_EXCHANGE_TYPE = "direct";
    String TOPIC_EXCHANGE_TYPE = "topic";
    String FANOUT_EXCHANGE_TYPE = "fanout";
    String HEADERS_EXCHANGE_TYPE = "headers";
    String DELAYED_EXCHANGE_TYPE = "x-delayed-message";
    String CONSISTENT_HASH_EXCHANGE_TYPE = "x-consistent-hash";
    
    // === 新增通用交换机名称 ===
    String COMMON_DIRECT_EXCHANGE = "common.direct.exchange";
    String COMMON_TOPIC_EXCHANGE = "common.topic.exchange";
    String COMMON_FANOUT_EXCHANGE = "common.fanout.exchange";
    
    // === 新增通用队列名称 ===
    String COMMON_DIRECT_QUEUE = "common.direct.queue";
    String COMMON_TOPIC_QUEUE = "common.topic.queue";
    String COMMON_FANOUT_QUEUE = "common.fanout.queue";
}
