package com.lanshan.base.api.enums;

import lombok.Getter;

/**
 * 用户类型枚举
 */
@Getter
public enum UserTypeEnum {

    OTHER(0, "其他"),
    TEACHER(1, "教职工"),
    UNDERGRADUATE(2, "本科生"),
    GRADUATE(3, "研究生"),
    NEW_STUDENT(4, "新生"),
    ;

    private final Integer code;
    private final String desc;

    UserTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (UserTypeEnum value : UserTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    public static String getDescByCodeStr(String code) {
        for (UserTypeEnum value : UserTypeEnum.values()) {
            if (value.code.equals(Integer.parseInt(code))) {
                return value.desc;
            }
        }
        return null;
    }

    public static Integer getCodeByDesc(String desc) {
        for (UserTypeEnum value : UserTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }

    public static String getCodeStrByDesc(String desc) {
        for (UserTypeEnum value : UserTypeEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.code.toString();
            }
        }
        return null;
    }
}
