package com.lanshan.app.reservation.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.reservation.converter.RsvVenueFieldTypeConverter;
import com.lanshan.app.reservation.entity.RsvVenueFieldType;
import com.lanshan.app.reservation.service.RsvVenueFieldTypeService;
import com.lanshan.app.reservation.vo.RsvVenueFieldTypeVO;
import com.lanshan.base.api.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 场馆预约场地类型表(RsvVenueFieldType)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("rsvVenueFieldType")
@Api(tags = "场馆预约场地类型表(RsvVenueFieldType)控制层", hidden = true)
public class RsvVenueFieldTypeController {
    /**
     * 服务对象
     */
    @Resource
    private RsvVenueFieldTypeService rsvVenueFieldTypeService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<RsvVenueFieldTypeVO>> page(Page<RsvVenueFieldTypeVO> page, RsvVenueFieldTypeVO vo) {

        IPage<RsvVenueFieldType> pageData = this.rsvVenueFieldTypeService.page(page.convert(RsvVenueFieldTypeConverter.INSTANCE::toEntity), Wrappers.lambdaQuery(RsvVenueFieldType.class)
                .like(StringUtils.isNotBlank(vo.getName()), RsvVenueFieldType::getName, vo.getName())
                .eq(Objects.nonNull(vo.getStatus()), RsvVenueFieldType::getStatus, vo.getStatus())
                .orderByDesc(RsvVenueFieldType::getId));
        return Result.build(pageData.convert(RsvVenueFieldTypeConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<RsvVenueFieldTypeVO> selectOne(@PathVariable Serializable id) {
        return Result.build(RsvVenueFieldTypeConverter.INSTANCE.toVO(this.rsvVenueFieldTypeService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody RsvVenueFieldTypeVO vo) {
        return Result.build(this.rsvVenueFieldTypeService.save(RsvVenueFieldTypeConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody RsvVenueFieldTypeVO vo) {
        return Result.build(this.rsvVenueFieldTypeService.updateById(RsvVenueFieldTypeConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.rsvVenueFieldTypeService.removeByIds(idList));
    }
}

