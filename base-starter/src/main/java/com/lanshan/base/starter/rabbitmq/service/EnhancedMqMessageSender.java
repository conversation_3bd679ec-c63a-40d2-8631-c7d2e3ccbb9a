package com.lanshan.base.starter.rabbitmq.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 增强的MQ消息发送服务
 * 支持各种类型交换机的消息发送
 */
@Slf4j
@Service
public class EnhancedMqMessageSender {
    
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息到Direct交换机
     */
    public void sendToDirectExchange(String exchange, String routingKey, Object message) {
        sendMessage(exchange, routingKey, message);
    }

    /**
     * 发送消息到Topic交换机
     */
    public void sendToTopicExchange(String exchange, String routingKey, Object message) {
        sendMessage(exchange, routingKey, message);
    }

    /**
     * 发送消息到Fanout交换机
     */
    public void sendToFanoutExchange(String exchange, Object message) {
        sendMessage(exchange, "", message);
    }

    /**
     * 发送消息到Headers交换机
     */
    public void sendToHeadersExchange(String exchange, Object message, Map<String, Object> headers) {
        MessagePostProcessor processor = msg -> {
            MessageProperties properties = msg.getMessageProperties();
            headers.forEach(properties::setHeader);
            return msg;
        };
        
        sendCustomMessage(exchange, "", message, processor);
    }

    /**
     * 发送延时消息（使用x-delayed-message交换机）
     */
    public void sendDelayedMessage(String exchange, String routingKey, Object message, long delay, TimeUnit timeUnit) {
        MessagePostProcessor processor = msg -> {
            long delayMillis = timeUnit.toMillis(delay);
            msg.getMessageProperties().setHeader("x-delay", delayMillis);
            return msg;
        };
        
        sendCustomMessage(exchange, routingKey, message, processor);
        log.debug("延时消息发送成功: exchange={}, routingKey={}, delay={}ms", exchange, routingKey, timeUnit.toMillis(delay));
    }

    /**
     * 发送消息到一致性哈希交换机
     */
    public void sendToConsistentHashExchange(String exchange, String hashKey, Object message) {
        MessagePostProcessor processor = msg -> {
            msg.getMessageProperties().setHeader("hash-on", hashKey);
            return msg;
        };
        
        sendCustomMessage(exchange, "", message, processor);
    }

    /**
     * 发送带优先级的消息
     */
    public void sendMessageWithPriority(String exchange, String routingKey, Object message, int priority) {
        MessagePostProcessor processor = msg -> {
            msg.getMessageProperties().setPriority(priority);
            return msg;
        };
        
        sendCustomMessage(exchange, routingKey, message, processor);
        log.debug("优先级消息发送成功: exchange={}, routingKey={}, priority={}", exchange, routingKey, priority);
    }

    /**
     * 发送带过期时间的消息
     */
    public void sendMessageWithExpiration(String exchange, String routingKey, Object message, long expiration, TimeUnit timeUnit) {
        MessagePostProcessor processor = msg -> {
            msg.getMessageProperties().setExpiration(String.valueOf(timeUnit.toMillis(expiration)));
            return msg;
        };
        
        sendCustomMessage(exchange, routingKey, message, processor);
        log.debug("过期消息发送成功: exchange={}, routingKey={}, expiration={}ms", 
            exchange, routingKey, timeUnit.toMillis(expiration));
    }

    /**
     * 发送消息（基础方法）
     */
    public void sendMessage(String exchange, String routingKey, Object message) {
        try {
            rabbitTemplate.convertAndSend(exchange, routingKey, message);
            log.debug("消息发送成功: exchange={}, routingKey={}", exchange, routingKey);
        } catch (Exception e) {
            log.error("消息发送失败: exchange={}, routingKey={}, error={}", exchange, routingKey, e.getMessage());
            throw e;
        }
    }

    /**
     * 发送自定义消息
     */
    public void sendCustomMessage(String exchange, String routingKey, Object message, MessagePostProcessor processor) {
        try {
            rabbitTemplate.convertAndSend(exchange, routingKey, message, processor);
            log.debug("自定义消息发送成功: exchange={}, routingKey={}", exchange, routingKey);
        } catch (Exception e) {
            log.error("自定义消息发送失败: exchange={}, routingKey={}, error={}", exchange, routingKey, e.getMessage());
            throw e;
        }
    }

    /**
     * 发送消息并等待回复（RPC模式）
     */
    public Object sendAndReceive(String exchange, String routingKey, Object message) {
        try {
            Object reply = rabbitTemplate.convertSendAndReceive(exchange, routingKey, message);
            log.debug("RPC消息发送成功: exchange={}, routingKey={}", exchange, routingKey);
            return reply;
        } catch (Exception e) {
            log.error("RPC消息发送失败: exchange={}, routingKey={}, error={}", exchange, routingKey, e.getMessage());
            throw e;
        }
    }
} 