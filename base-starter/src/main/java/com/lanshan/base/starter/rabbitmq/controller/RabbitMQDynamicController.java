package com.lanshan.base.starter.rabbitmq.controller;

import com.lanshan.base.starter.rabbitmq.properties.RabbitMQProperties;
import com.lanshan.base.starter.rabbitmq.service.DynamicRabbitMQConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * RabbitMQ动态配置控制器
 * 提供运行时动态管理RabbitMQ配置的API
 */
@Slf4j
@RestController
@RequestMapping("/rabbitmq/dynamic")
@Api(tags = "RabbitMQ动态配置管理")
@ConditionalOnProperty(prefix = "spring.rabbitmq.custom", name = "management.enabled", havingValue = "true")
public class RabbitMQDynamicController {

    @Resource
    private DynamicRabbitMQConfigService dynamicConfigService;

    @PostMapping("/refresh")
    @ApiOperation("刷新RabbitMQ配置")
    public String refreshConfiguration() {
        try {
            dynamicConfigService.refreshConfiguration();
            return "RabbitMQ配置刷新成功";
        } catch (Exception e) {
            log.error("RabbitMQ配置刷新失败", e);
            return "RabbitMQ配置刷新失败: " + e.getMessage();
        }
    }

    @PostMapping("/queue")
    @ApiOperation("动态创建队列")
    public String createQueue(@RequestBody RabbitMQProperties.QueueConfig queueConfig) {
        try {
            dynamicConfigService.createQueue(queueConfig);
            return "队列创建成功: " + queueConfig.getName();
        } catch (Exception e) {
            log.error("队列创建失败", e);
            return "队列创建失败: " + e.getMessage();
        }
    }

    @PostMapping("/exchange")
    @ApiOperation("动态创建交换机")
    public String createExchange(@RequestBody RabbitMQProperties.ExchangeConfig exchangeConfig) {
        try {
            dynamicConfigService.createExchange(exchangeConfig);
            return "交换机创建成功: " + exchangeConfig.getName();
        } catch (Exception e) {
            log.error("交换机创建失败", e);
            return "交换机创建失败: " + e.getMessage();
        }
    }

    @PostMapping("/binding")
    @ApiOperation("动态创建绑定关系")
    public String createBinding(@RequestBody RabbitMQProperties.BindingConfig bindingConfig) {
        try {
            dynamicConfigService.createBinding(bindingConfig);
            return "绑定关系创建成功";
        } catch (Exception e) {
            log.error("绑定关系创建失败", e);
            return "绑定关系创建失败: " + e.getMessage();
        }
    }

    @PostMapping("/clear-cache")
    @ApiOperation("清理缓存")
    public String clearCache() {
        try {
            dynamicConfigService.clearCache();
            return "缓存清理成功";
        } catch (Exception e) {
            log.error("缓存清理失败", e);
            return "缓存清理失败: " + e.getMessage();
        }
    }
} 